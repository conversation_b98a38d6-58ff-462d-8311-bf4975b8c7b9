[{"/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/layout.tsx": "1", "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/page.tsx": "2", "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/layout.tsx": "3", "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/page.tsx": "4", "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/ui/button.tsx": "5", "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/ai.ts": "6", "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/env.ts": "7", "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/s3.ts": "8", "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/utils.ts": "9"}, {"size": 205, "mtime": 1752848752685, "results": "10", "hashOfConfig": "11"}, {"size": 1739, "mtime": 1752848933992, "results": "12", "hashOfConfig": "11"}, {"size": 382, "mtime": 1752799340733, "results": "13", "hashOfConfig": "11"}, {"size": 286, "mtime": 1752847934261, "results": "14", "hashOfConfig": "11"}, {"size": 2123, "mtime": 1752847917527, "results": "15", "hashOfConfig": "11"}, {"size": 2617, "mtime": 1752848987683, "results": "16", "hashOfConfig": "11"}, {"size": 1306, "mtime": 1752848741818, "results": "17", "hashOfConfig": "11"}, {"size": 1807, "mtime": 1752848922508, "results": "18", "hashOfConfig": "11"}, {"size": 166, "mtime": 1752847892499, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "ovuwc4", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/layout.tsx", [], [], "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/[slug]/page.tsx", [], [], "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/page.tsx", [], [], "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/ui/button.tsx", [], [], "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/ai.ts", [], [], "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/env.ts", [], [], "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/s3.ts", [], [], "/Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/utils.ts", [], []]