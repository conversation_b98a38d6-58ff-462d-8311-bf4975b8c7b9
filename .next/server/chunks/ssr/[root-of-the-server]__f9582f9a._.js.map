{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 27, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,qZAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function Home() {\n  const [input, setInput] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const router = useRouter();\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim()) return;\n\n    setIsLoading(true);\n    // Clean the input for URL usage\n    const cleanInput = input\n      .trim()\n      .replace(/[^a-zA-Z0-9\\s\\-_]/g, \"\")\n      .replace(/\\s+/g, \"-\");\n    router.push(`/${cleanInput}`);\n  };\n\n  const examples = [\n    \"portfolio-website\",\n    \"restaurant-menu\",\n    \"landing-page-for-saas\",\n    \"personal-blog\",\n    \"event-invitation\",\n    \"product-showcase\",\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\n      {/* Header */}\n      <header className=\"pt-8 pb-4 text-center\">\n        <div className=\"max-w-4xl mx-auto px-6\">\n          <h1 className=\"text-5xl font-bold text-gray-900 mb-4\">\n            Every Website AI\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Generate any webpage instantly with AI. Just describe what you want,\n            and we'll create it for you.\n          </p>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1 flex items-center justify-center px-6 py-12\">\n        <div className=\"max-w-2xl w-full space-y-8\">\n          {/* Input Section */}\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label\n                  htmlFor=\"webpage-input\"\n                  className=\"block text-lg font-semibold text-gray-800 mb-3\"\n                >\n                  What webpage do you want to create?\n                </label>\n                <input\n                  id=\"webpage-input\"\n                  type=\"text\"\n                  value={input}\n                  onChange={(e) => setInput(e.target.value)}\n                  placeholder=\"e.g., portfolio website, restaurant menu, landing page...\"\n                  className=\"w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all\"\n                  disabled={isLoading}\n                />\n              </div>\n              <Button\n                type=\"submit\"\n                className=\"w-full py-3 text-lg font-semibold\"\n                disabled={!input.trim() || isLoading}\n              >\n                {isLoading ? \"Generating...\" : \"Generate Webpage\"}\n              </Button>\n            </form>\n          </div>\n\n          {/* Examples */}\n          <div className=\"text-center\">\n            <p className=\"text-gray-600 mb-4\">Try these examples:</p>\n            <div className=\"flex flex-wrap gap-2 justify-center\">\n              {examples.map((example) => (\n                <button\n                  key={example}\n                  onClick={() => setInput(example.replace(/-/g, \" \"))}\n                  className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors\"\n                  disabled={isLoading}\n                >\n                  {example.replace(/-/g, \" \")}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* How it works */}\n      <section className=\"py-16 bg-white/50\">\n        <div className=\"max-w-4xl mx-auto px-6\">\n          <h2 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">\n            How it works\n          </h2>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 font-bold text-lg\">1</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-800 mb-2\">Describe</h3>\n              <p className=\"text-gray-600 text-sm\">\n                Tell us what kind of webpage you want to create\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 font-bold text-lg\">2</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-800 mb-2\">Generate</h3>\n              <p className=\"text-gray-600 text-sm\">\n                Our AI creates a complete, responsive webpage for you\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 font-bold text-lg\">3</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-800 mb-2\">Use</h3>\n              <p className=\"text-gray-600 text-sm\">\n                Your webpage is ready to use instantly\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"py-8 text-center text-gray-500 text-sm\">\n        <p>\n          Powered by{\" \"}\n          <a\n            href=\"https://dothistask.ai\"\n            className=\"underline hover:text-gray-700\"\n          >\n            dothistask.ai\n          </a>\n          {\" • \"}\n          <a\n            href=\"https://twitter.com/n3sonline\"\n            className=\"underline hover:text-gray-700\"\n          >\n            @n3sonline\n          </a>\n        </p>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4WAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,ySAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,aAAa;QACb,gCAAgC;QAChC,MAAM,aAAa,MAChB,IAAI,GACJ,OAAO,CAAC,sBAAsB,IAC9B,OAAO,CAAC,QAAQ;QACnB,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY;IAC9B;IAEA,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,qZAAC;QAAI,WAAU;;0BAEb,qZAAC;gBAAO,WAAU;0BAChB,cAAA,qZAAC;oBAAI,WAAU;;sCACb,qZAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,qZAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAQ3D,qZAAC;gBAAK,WAAU;0BACd,cAAA,qZAAC;oBAAI,WAAU;;sCAEb,qZAAC;4BAAI,WAAU;sCACb,cAAA,qZAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,qZAAC;;0DACC,qZAAC;gDACC,SAAQ;gDACR,WAAU;0DACX;;;;;;0DAGD,qZAAC;gDACC,IAAG;gDACH,MAAK;gDACL,OAAO;gDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gDACxC,aAAY;gDACZ,WAAU;gDACV,UAAU;;;;;;;;;;;;kDAGd,qZAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,UAAU,CAAC,MAAM,IAAI,MAAM;kDAE1B,YAAY,kBAAkB;;;;;;;;;;;;;;;;;sCAMrC,qZAAC;4BAAI,WAAU;;8CACb,qZAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,qZAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,qZAAC;4CAEC,SAAS,IAAM,SAAS,QAAQ,OAAO,CAAC,MAAM;4CAC9C,WAAU;4CACV,UAAU;sDAET,QAAQ,OAAO,CAAC,MAAM;2CALlB;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAcjB,qZAAC;gBAAQ,WAAU;0BACjB,cAAA,qZAAC;oBAAI,WAAU;;sCACb,qZAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,qZAAC;4BAAI,WAAU;;8CACb,qZAAC;oCAAI,WAAU;;sDACb,qZAAC;4CAAI,WAAU;sDACb,cAAA,qZAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,qZAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,qZAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,qZAAC;oCAAI,WAAU;;sDACb,qZAAC;4CAAI,WAAU;sDACb,cAAA,qZAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,qZAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,qZAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,qZAAC;oCAAI,WAAU;;sDACb,qZAAC;4CAAI,WAAU;sDACb,cAAA,qZAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,qZAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,qZAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,qZAAC;gBAAO,WAAU;0BAChB,cAAA,qZAAC;;wBAAE;wBACU;sCACX,qZAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;wBAGA;sCACD,qZAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}]}