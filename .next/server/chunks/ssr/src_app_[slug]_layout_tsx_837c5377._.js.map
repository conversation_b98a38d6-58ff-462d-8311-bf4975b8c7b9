{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/%5Bslug%5D/layout.tsx"], "sourcesContent": ["export default function SlugLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <div style={{ width: '100%', height: '100vh', overflow: 'auto' }}>\n      {children}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS,WAAW,EACjC,QAAQ,EAGT;IACC,qBACE,qZAAC;QAAI,OAAO;YAAE,OAAO;YAAQ,QAAQ;YAAS,UAAU;QAAO;kBAC5D;;;;;;AAGP", "debugId": null}}]}