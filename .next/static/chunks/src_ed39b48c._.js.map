{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,oWAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { useState } from \"react\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function Home() {\n  const [input, setInput] = useState(\"\");\n  const [isLoading, setIsLoading] = useState(false);\n  const router = useRouter();\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim()) return;\n\n    setIsLoading(true);\n    // Clean the input for URL usage\n    const cleanInput = input\n      .trim()\n      .replace(/[^a-zA-Z0-9\\s\\-_]/g, \"\")\n      .replace(/\\s+/g, \"-\");\n    router.push(`/${cleanInput}`);\n  };\n\n  const examples = [\n    \"portfolio-website\",\n    \"restaurant-menu\",\n    \"landing-page-for-saas\",\n    \"personal-blog\",\n    \"event-invitation\",\n    \"product-showcase\",\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50\">\n      {/* Header */}\n      <header className=\"pt-8 pb-4 text-center\">\n        <div className=\"max-w-4xl mx-auto px-6\">\n          <h1 className=\"text-5xl font-bold text-gray-900 mb-4\">\n            Every Website AI\n          </h1>\n          <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n            Generate any webpage instantly with AI. Just change the URL to\n            describe what you want.\n          </p>\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200 max-w-xl mx-auto\">\n            <p className=\"text-blue-800 font-mono text-lg\">\n              everywebsite.ai/\n              <span className=\"bg-blue-200 px-2 py-1 rounded\">\n                your-prompt-here\n              </span>\n            </p>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"flex-1 flex items-center justify-center px-6 py-12\">\n        <div className=\"max-w-2xl w-full space-y-8\">\n          {/* Input Section */}\n          <div className=\"bg-white rounded-2xl shadow-xl p-8 border border-gray-100\">\n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div>\n                <label\n                  htmlFor=\"webpage-input\"\n                  className=\"block text-lg font-semibold text-gray-800 mb-3\"\n                >\n                  Try it now - enter your prompt:\n                </label>\n                <div className=\"relative\">\n                  <div className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-mono\">\n                    everywebsite.ai/\n                  </div>\n                  <input\n                    id=\"webpage-input\"\n                    type=\"text\"\n                    value={input}\n                    onChange={(e) => setInput(e.target.value)}\n                    placeholder=\"portfolio-website\"\n                    className=\"w-full pl-32 pr-4 py-3 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all font-mono\"\n                    disabled={isLoading}\n                  />\n                </div>\n              </div>\n              <Button\n                type=\"submit\"\n                className=\"w-full py-3 text-lg font-semibold\"\n                disabled={!input.trim() || isLoading}\n              >\n                {isLoading ? \"Generating...\" : \"Generate Webpage\"}\n              </Button>\n            </form>\n          </div>\n\n          {/* Examples */}\n          <div className=\"text-center\">\n            <p className=\"text-gray-600 mb-4\">Try these example URLs:</p>\n            <div className=\"flex flex-wrap gap-2 justify-center\">\n              {examples.map((example) => (\n                <button\n                  key={example}\n                  onClick={() => setInput(example)}\n                  className=\"px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors font-mono\"\n                  disabled={isLoading}\n                >\n                  /{example}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n      </main>\n\n      {/* How it works */}\n      <section className=\"py-16 bg-white/50\">\n        <div className=\"max-w-4xl mx-auto px-6\">\n          <h2 className=\"text-3xl font-bold text-center text-gray-900 mb-12\">\n            How it works\n          </h2>\n          <div className=\"grid md:grid-cols-3 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 font-bold text-lg\">1</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-800 mb-2\">\n                Change the URL\n              </h3>\n              <p className=\"text-gray-600 text-sm\">\n                Add your prompt to the URL: everywebsite.ai/your-idea\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 font-bold text-lg\">2</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-800 mb-2\">AI Generates</h3>\n              <p className=\"text-gray-600 text-sm\">\n                Our AI instantly creates a complete, responsive webpage\n              </p>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                <span className=\"text-blue-600 font-bold text-lg\">3</span>\n              </div>\n              <h3 className=\"font-semibold text-gray-800 mb-2\">That's it!</h3>\n              <p className=\"text-gray-600 text-sm\">\n                Your custom webpage is ready to use and share\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Footer */}\n      <footer className=\"py-8 text-center text-gray-500 text-sm\">\n        <p>\n          Powered by{\" \"}\n          <a\n            href=\"https://dothistask.ai\"\n            className=\"underline hover:text-gray-700\"\n          >\n            dothistask.ai\n          </a>\n          {\" • \"}\n          <a\n            href=\"https://twitter.com/n3sonline\"\n            className=\"underline hover:text-gray-700\"\n          >\n            @n3sonline\n          </a>\n        </p>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oUAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,4SAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,CAAC,MAAM,IAAI,IAAI;QAEnB,aAAa;QACb,gCAAgC;QAChC,MAAM,aAAa,MAChB,IAAI,GACJ,OAAO,CAAC,sBAAsB,IAC9B,OAAO,CAAC,QAAQ;QACnB,OAAO,IAAI,CAAC,AAAC,IAAc,OAAX;IAClB;IAEA,MAAM,WAAW;QACf;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,oWAAC;QAAI,WAAU;;0BAEb,oWAAC;gBAAO,WAAU;0BAChB,cAAA,oWAAC;oBAAI,WAAU;;sCACb,oWAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,oWAAC;4BAAE,WAAU;sCAA0C;;;;;;sCAIvD,oWAAC;4BAAI,WAAU;sCACb,cAAA,oWAAC;gCAAE,WAAU;;oCAAkC;kDAE7C,oWAAC;wCAAK,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASxD,oWAAC;gBAAK,WAAU;0BACd,cAAA,oWAAC;oBAAI,WAAU;;sCAEb,oWAAC;4BAAI,WAAU;sCACb,cAAA,oWAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,oWAAC;;0DACC,oWAAC;gDACC,SAAQ;gDACR,WAAU;0DACX;;;;;;0DAGD,oWAAC;gDAAI,WAAU;;kEACb,oWAAC;wDAAI,WAAU;kEAA6E;;;;;;kEAG5F,oWAAC;wDACC,IAAG;wDACH,MAAK;wDACL,OAAO;wDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wDACxC,aAAY;wDACZ,WAAU;wDACV,UAAU;;;;;;;;;;;;;;;;;;kDAIhB,oWAAC,qIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,UAAU,CAAC,MAAM,IAAI,MAAM;kDAE1B,YAAY,kBAAkB;;;;;;;;;;;;;;;;;sCAMrC,oWAAC;4BAAI,WAAU;;8CACb,oWAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAClC,oWAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC,wBACb,oWAAC;4CAEC,SAAS,IAAM,SAAS;4CACxB,WAAU;4CACV,UAAU;;gDACX;gDACG;;2CALG;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAcjB,oWAAC;gBAAQ,WAAU;0BACjB,cAAA,oWAAC;oBAAI,WAAU;;sCACb,oWAAC;4BAAG,WAAU;sCAAqD;;;;;;sCAGnE,oWAAC;4BAAI,WAAU;;8CACb,oWAAC;oCAAI,WAAU;;sDACb,oWAAC;4CAAI,WAAU;sDACb,cAAA,oWAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,oWAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,oWAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,oWAAC;oCAAI,WAAU;;sDACb,oWAAC;4CAAI,WAAU;sDACb,cAAA,oWAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,oWAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,oWAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAIvC,oWAAC;oCAAI,WAAU;;sDACb,oWAAC;4CAAI,WAAU;sDACb,cAAA,oWAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,oWAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,oWAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,oWAAC;gBAAO,WAAU;0BAChB,cAAA,oWAAC;;wBAAE;wBACU;sCACX,oWAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;wBAGA;sCACD,oWAAC;4BACC,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAxKwB;;QAGP,4SAAA,CAAA,YAAS;;;KAHF", "debugId": null}}]}