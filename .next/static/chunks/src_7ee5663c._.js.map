{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,8OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,KASb;QATa,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF,GATa;IAUd,MAAM,OAAO,UAAU,uSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,oWAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/Projects/N3S/every-website-ai/src/app/%5Bslug%5D/error.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\n\ninterface ErrorPageProps {\n  error: Error & { digest?: string };\n  reset: () => void;\n}\n\nexport default function ErrorPage({ error, reset }: ErrorPageProps) {\n  return (\n    <div className=\"flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-red-50 to-orange-100 p-8\">\n      <div className=\"text-center space-y-6 max-w-md\">\n        {/* Error icon */}\n        <div className=\"w-16 h-16 mx-auto bg-red-100 rounded-full flex items-center justify-center\">\n          <svg\n            className=\"w-8 h-8 text-red-600\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n            />\n          </svg>\n        </div>\n\n        {/* Error message */}\n        <div className=\"space-y-2\">\n          <h1 className=\"text-2xl font-bold text-gray-800\">\n            Oops! Something went wrong\n          </h1>\n          <p className=\"text-gray-600\">\n            We couldn't generate your webpage right now. This might be a temporary issue.\n          </p>\n        </div>\n\n        {/* Action buttons */}\n        <div className=\"space-y-4\">\n          <div className=\"flex flex-col sm:flex-row gap-3 justify-center\">\n            <Button onClick={reset} className=\"px-6\">\n              Try Again\n            </Button>\n            <Button\n              variant=\"outline\"\n              onClick={() => window.location.href = \"/\"}\n              className=\"px-6\"\n            >\n              Go Home\n            </Button>\n          </div>\n\n          {/* Contact info */}\n          <div className=\"pt-4 border-t border-gray-200\">\n            <p className=\"text-sm text-gray-500 mb-3\">\n              Still having issues? Come back later or reach out:\n            </p>\n            <a\n              href=\"https://twitter.com/n3sonline\"\n              target=\"_blank\"\n              rel=\"noopener noreferrer\"\n              className=\"inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 transition-colors\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\" />\n              </svg>\n              Tweet @n3sonline\n            </a>\n          </div>\n        </div>\n\n        {/* Powered by notice */}\n        <div className=\"mt-8 text-xs text-gray-500\">\n          Powered by{\" \"}\n          <a href=\"https://dothistask.ai\" className=\"underline\">\n            dothistask.ai\n          </a>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AASe,SAAS,UAAU,KAAgC;QAAhC,EAAE,KAAK,EAAE,KAAK,EAAkB,GAAhC;IAChC,qBACE,oWAAC;QAAI,WAAU;kBACb,cAAA,oWAAC;YAAI,WAAU;;8BAEb,oWAAC;oBAAI,WAAU;8BACb,cAAA,oWAAC;wBACC,WAAU;wBACV,MAAK;wBACL,QAAO;wBACP,SAAQ;kCAER,cAAA,oWAAC;4BACC,eAAc;4BACd,gBAAe;4BACf,aAAa;4BACb,GAAE;;;;;;;;;;;;;;;;8BAMR,oWAAC;oBAAI,WAAU;;sCACb,oWAAC;4BAAG,WAAU;sCAAmC;;;;;;sCAGjD,oWAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAM/B,oWAAC;oBAAI,WAAU;;sCACb,oWAAC;4BAAI,WAAU;;8CACb,oWAAC,qIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAO,WAAU;8CAAO;;;;;;8CAGzC,oWAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;oCACtC,WAAU;8CACX;;;;;;;;;;;;sCAMH,oWAAC;4BAAI,WAAU;;8CACb,oWAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,oWAAC;oCACC,MAAK;oCACL,QAAO;oCACP,KAAI;oCACJ,WAAU;;sDAEV,oWAAC;4CAAI,WAAU;4CAAU,MAAK;4CAAe,SAAQ;sDACnD,cAAA,oWAAC;gDAAK,GAAE;;;;;;;;;;;wCACJ;;;;;;;;;;;;;;;;;;;8BAOZ,oWAAC;oBAAI,WAAU;;wBAA6B;wBAC/B;sCACX,oWAAC;4BAAE,MAAK;4BAAwB,WAAU;sCAAY;;;;;;;;;;;;;;;;;;;;;;;AAOhE;KA3EwB", "debugId": null}}]}