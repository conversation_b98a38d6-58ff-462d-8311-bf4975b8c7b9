"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { useRouter } from "next/navigation";

export default function Home() {
  const [input, setInput] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim()) return;

    setIsLoading(true);
    // Clean the input for URL usage
    const cleanInput = input
      .trim()
      .replace(/[^a-zA-Z0-9\s\-_]/g, "")
      .replace(/\s+/g, "-");
    router.push(`/${cleanInput}`);
  };

  const examples = [
    "portfolio-website",
    "restaurant-menu",
    "landing-page-for-saas",
    "personal-blog",
    "event-invitation",
    "product-showcase",
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      {/* Header */}
      <header className="pt-8 pb-4 text-center">
        <div className="max-w-4xl mx-auto px-6">
          <h1 className="text-5xl font-bold text-gray-900 mb-4">
            Every Website AI
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Generate any webpage instantly with AI. Just describe what you want,
            and we'll create it for you.
          </p>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center px-6 py-12">
        <div className="max-w-2xl w-full space-y-8">
          {/* Input Section */}
          <div className="bg-white rounded-2xl shadow-xl p-8 border border-gray-100">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <label
                  htmlFor="webpage-input"
                  className="block text-lg font-semibold text-gray-800 mb-3"
                >
                  What webpage do you want to create?
                </label>
                <input
                  id="webpage-input"
                  type="text"
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  placeholder="e.g., portfolio website, restaurant menu, landing page..."
                  className="w-full px-4 py-3 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none transition-all"
                  disabled={isLoading}
                />
              </div>
              <Button
                type="submit"
                className="w-full py-3 text-lg font-semibold"
                disabled={!input.trim() || isLoading}
              >
                {isLoading ? "Generating..." : "Generate Webpage"}
              </Button>
            </form>
          </div>

          {/* Examples */}
          <div className="text-center">
            <p className="text-gray-600 mb-4">Try these examples:</p>
            <div className="flex flex-wrap gap-2 justify-center">
              {examples.map((example) => (
                <button
                  key={example}
                  onClick={() => setInput(example.replace(/-/g, " "))}
                  className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded-full transition-colors"
                  disabled={isLoading}
                >
                  {example.replace(/-/g, " ")}
                </button>
              ))}
            </div>
          </div>
        </div>
      </main>

      {/* How it works */}
      <section className="py-16 bg-white/50">
        <div className="max-w-4xl mx-auto px-6">
          <h2 className="text-3xl font-bold text-center text-gray-900 mb-12">
            How it works
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 font-bold text-lg">1</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">Describe</h3>
              <p className="text-gray-600 text-sm">
                Tell us what kind of webpage you want to create
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 font-bold text-lg">2</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">Generate</h3>
              <p className="text-gray-600 text-sm">
                Our AI creates a complete, responsive webpage for you
              </p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <span className="text-blue-600 font-bold text-lg">3</span>
              </div>
              <h3 className="font-semibold text-gray-800 mb-2">Use</h3>
              <p className="text-gray-600 text-sm">
                Your webpage is ready to use instantly
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-8 text-center text-gray-500 text-sm">
        <p>
          Powered by{" "}
          <a
            href="https://dothistask.ai"
            className="underline hover:text-gray-700"
          >
            dothistask.ai
          </a>
          {" • "}
          <a
            href="https://twitter.com/n3sonline"
            className="underline hover:text-gray-700"
          >
            @n3sonline
          </a>
        </p>
      </footer>
    </div>
  );
}
